# Visual Studio Cleanup Script
# Run as Administrator

param(
    [switch]$WhatIf = $false
)

Write-Host "=== Visual Studio Cleanup Script ===" -ForegroundColor Green
Write-Host "This script will help clean up old Visual Studio installations" -ForegroundColor Yellow

if (-not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges. Please run as Administrator." -ForegroundColor Red
    exit 1
}

if ($WhatIf) {
    Write-Host "Running in WhatIf mode - no changes will be made" -ForegroundColor Cyan
}

# Function to safely remove directory
function Remove-DirectorySafely {
    param($Path, $Description)
    
    if (Test-Path $Path) {
        Write-Host "Found: $Description at $Path" -ForegroundColor Yellow
        
        if ($WhatIf) {
            Write-Host "  [WhatIf] Would remove: $Path" -ForegroundColor Cyan
        } else {
            $confirm = Read-Host "Remove $Description? (y/N)"
            if ($confirm -eq 'y' -or $confirm -eq 'Y') {
                try {
                    Remove-Item $Path -Recurse -Force
                    Write-Host "  ✓ Removed: $Path" -ForegroundColor Green
                } catch {
                    Write-Host "  ✗ Failed to remove: $($_.Exception.Message)" -ForegroundColor Red
                }
            } else {
                Write-Host "  Skipped: $Path" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "Not found: $Description" -ForegroundColor Gray
    }
}

# Function to uninstall via Programs and Features
function Uninstall-Program {
    param($DisplayName, $UninstallString)
    
    if ($UninstallString) {
        Write-Host "Found program: $DisplayName" -ForegroundColor Yellow
        
        if ($WhatIf) {
            Write-Host "  [WhatIf] Would uninstall: $DisplayName" -ForegroundColor Cyan
        } else {
            $confirm = Read-Host "Uninstall $DisplayName? (y/N)"
            if ($confirm -eq 'y' -or $confirm -eq 'Y') {
                try {
                    Write-Host "  Starting uninstall: $DisplayName" -ForegroundColor Yellow
                    Start-Process -FilePath "cmd.exe" -ArgumentList "/c", $UninstallString -Wait
                    Write-Host "  ✓ Uninstall completed: $DisplayName" -ForegroundColor Green
                } catch {
                    Write-Host "  ✗ Failed to uninstall: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    }
}

Write-Host "`n1. Checking for Visual Studio 2015..." -ForegroundColor Yellow
Remove-DirectorySafely "C:\Program Files (x86)\Microsoft Visual Studio 14.0" "Visual Studio 2015"

Write-Host "`n2. Checking for VS 2022 remnants in x86 folder..." -ForegroundColor Yellow
Remove-DirectorySafely "C:\Program Files (x86)\Microsoft Visual Studio\2022" "VS 2022 remnants"

Write-Host "`n3. Checking installed programs..." -ForegroundColor Yellow
try {
    $programs = Get-ItemProperty "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*" | 
                Where-Object { $_.DisplayName -like "*Visual Studio*" -and $_.DisplayName -notlike "*2022*" -and $_.DisplayName -notlike "*Installer*" }
    
    foreach ($program in $programs) {
        if ($program.DisplayName -like "*2015*" -or $program.DisplayName -like "*14.0*") {
            Uninstall-Program $program.DisplayName $program.UninstallString
        }
    }
} catch {
    Write-Host "Error checking programs: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n4. Preserving VS 2022 Community..." -ForegroundColor Green
if (Test-Path "e:\Program Files\Microsoft Visual Studio\2022\Community") {
    Write-Host "  ✓ VS 2022 Community is preserved at: e:\Program Files\Microsoft Visual Studio\2022\Community" -ForegroundColor Green
} else {
    Write-Host "  ⚠️  Warning: VS 2022 Community not found at expected location!" -ForegroundColor Red
}

Write-Host "`n=== Cleanup Summary ===" -ForegroundColor Green
Write-Host "Cleanup completed. Run the verification script to check results:" -ForegroundColor Yellow
Write-Host "  powershell -ExecutionPolicy Bypass -File check_vs_installations.ps1" -ForegroundColor Cyan

if ($WhatIf) {
    Write-Host "`nTo actually perform the cleanup, run without -WhatIf parameter" -ForegroundColor Yellow
}
