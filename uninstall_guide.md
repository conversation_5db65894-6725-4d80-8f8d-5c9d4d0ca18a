# Visual Studio 清理指南

## 当前状况
- ✅ **保留**: Visual Studio 2022 Community (e:\Program Files\Microsoft Visual Studio\2022\Community)
- ❌ **删除**: Visual Studio 2015 (C:\Program Files (x86)\Microsoft Visual Studio 14.0)
- ❌ **清理**: VS 2022残留文件 (C:\Program Files (x86)\Microsoft Visual Studio\2022)

## 方法1: 使用Visual Studio Installer (推荐)

### 步骤1: 打开Visual Studio Installer
1. 按 Win + R，输入: `"C:\Program Files (x86)\Microsoft Visual Studio\Installer\vs_installer.exe"`
2. 或者从开始菜单搜索 "Visual Studio Installer"

### 步骤2: 在Installer中卸载
- 如果看到VS 2015或其他不需要的版本，点击"卸载"
- 确保保留VS 2022 Community

## 方法2: 手动卸载VS 2015

### 步骤1: 使用控制面板
1. 打开"程序和功能" (appwiz.cpl)
2. 查找并卸载以下项目：
   - Microsoft Visual Studio Community 2015
   - Microsoft Visual Studio 2015 Shell
   - Microsoft Visual C++ 2015 Redistributable (如果不被其他程序使用)

### 步骤2: 清理残留文件
删除文件夹: `C:\Program Files (x86)\Microsoft Visual Studio 14.0`

## 方法3: 使用专用清理工具

Microsoft提供了官方的VS清理工具：
- 下载: https://github.com/Microsoft/VisualStudioUninstaller
- 这个工具可以彻底清理所有VS残留

## 清理后验证

运行以下命令验证清理效果：
```powershell
powershell -ExecutionPolicy Bypass -File check_vs_installations.ps1
```

## 注意事项

⚠️ **重要提醒**:
1. 清理前确保没有重要项目依赖VS 2015
2. 保留VS 2022 Community，因为它与CUDA 12.9兼容
3. 清理后可能需要重新配置一些开发环境
4. 建议先创建系统还原点

## CUDA开发环境验证

清理后，使用我们的编译脚本验证CUDA环境：
```batch
.\compile_vs2022.bat
```
