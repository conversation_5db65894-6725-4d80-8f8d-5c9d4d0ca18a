#include <iostream>
#include <vector>
#include <cstdio> // 用于 printf
#include <cmath> // 用于 fabs

#include <cuda_fp16.h>
#include <cuda_runtime.h>
#include <mma.h> // **必须包含WMMA头文件**

// 使用 nvcuda 命名空间
using namespace nvcuda;

// 矩阵维度和Tile大小保持不变
#define M 64
#define N 16
#define K 16
#define TILE_SIZE 16

// **使用WMMA的Tensor Core Kernel**
__global__ void matrixMulWmmaKernel(half* C, const half* A, const half* B, int m, int n, int k) {
    int blockRow = blockIdx.y;
    int blockCol = blockIdx.x;
    
    wmma::fragment<wmma::matrix_a, TILE_SIZE, TILE_SIZE, TILE_SIZE, half, wmma::row_major> a_frag;
    wmma::fragment<wmma::matrix_b, TILE_SIZE, TILE_SIZE, TILE_SIZE, half, wmma::col_major> b_frag;
    wmma::fragment<wmma::accumulator, TILE_SIZE, TILE_SIZE, TILE_SIZE, float> acc_frag;

    wmma::fill_fragment(acc_frag, 0.0f);

    __shared__ half tileA_s[TILE_SIZE * TILE_SIZE];
    __shared__ half tileB_s[TILE_SIZE * TILE_SIZE];
    
    for (int p = 0; p < k / TILE_SIZE; ++p) {
        int row = threadIdx.y;
        int col = threadIdx.x;
        
        tileA_s[row * TILE_SIZE + col] = A[(blockRow * TILE_SIZE + row) * k + (p * TILE_SIZE + col)];
        tileB_s[row * TILE_SIZE + col] = B[(p * TILE_SIZE + row) * n + (blockCol * TILE_SIZE + col)];

        __syncthreads();

        wmma::load_matrix_sync(a_frag, tileA_s, TILE_SIZE);
        wmma::load_matrix_sync(b_frag, tileB_s, TILE_SIZE);

        wmma::mma_sync(acc_frag, a_frag, b_frag, acc_frag);

        __syncthreads();
    }

    int C_row = blockRow * TILE_SIZE;
    int C_col = blockCol * TILE_SIZE;
    wmma::store_matrix_sync(C + C_row * n + C_col, acc_frag, n, wmma::mem_row_major);
}

void cpuMatrixMul(std::vector<float>& C, const std::vector<half>& A, const std::vector<half>& B) {
    for (int i = 0; i < M; ++i) {
        for (int j = 0; j < N; ++j) {
            float sum = 0.0f;
            for (int l = 0; l < K; ++l) {
                sum += __half2float(A[i * K + l]) * __half2float(B[l * N + j]);
            }
            C[i * N + j] = sum;
        }
    }
}

int main() {
    // **修改点**: 直接设置使用设备0，不再需要 findCudaDevice
    int deviceId = 0;
    cudaError_t err = cudaSetDevice(deviceId);
    if (err != cudaSuccess) {
        std::cerr << "Failed to set CUDA device " << deviceId << ", error: " << cudaGetErrorString(err) << std::endl;
        return 1;
    }
    printf("Using CUDA Device %d\n", deviceId);


    std::vector<half> h_A(M * K);
    std::vector<half> h_B(K * N);
    std::vector<half> h_C_from_gpu(M * N);

    for (int i = 0; i < M * K; ++i) h_A[i] = __float2half(static_cast<float>((i % 10) + 1));
    for (int i = 0; i < K * N; ++i) h_B[i] = __float2half(static_cast<float>((i % 5) + 1));

    half *d_A, *d_B, *d_C;
    cudaMalloc(&d_A, M * K * sizeof(half));
    cudaMalloc(&d_B, K * N * sizeof(half));
    cudaMalloc(&d_C, M * N * sizeof(half));

    cudaMemcpy(d_A, h_A.data(), M * K * sizeof(half), cudaMemcpyHostToDevice);
    cudaMemcpy(d_B, h_B.data(), K * N * sizeof(half), cudaMemcpyHostToDevice);

    dim3 threadsPerBlock(TILE_SIZE, TILE_SIZE);
    dim3 numBlocks((N + TILE_SIZE - 1) / TILE_SIZE, (M + TILE_SIZE - 1) / TILE_SIZE);

    std::cout << "Launching WMMA Kernel with Grid size: (" << numBlocks.x << ", " << numBlocks.y << ")"
              << " and Block size: (" << threadsPerBlock.x << ", " << threadsPerBlock.y << ")" << std::endl;

    matrixMulWmmaKernel<<<numBlocks, threadsPerBlock>>>(d_C, d_A, d_B, M, N, K);

    err = cudaGetLastError();
    if (err != cudaSuccess) {
        std::cerr << "CUDA Kernel launch error: " << cudaGetErrorString(err) << std::endl;
        return 1;
    }
    cudaDeviceSynchronize();

    cudaMemcpy(h_C_from_gpu.data(), d_C, M * N * sizeof(half), cudaMemcpyDeviceToHost);

    std::cout << "Verifying result..." << std::endl;
    std::vector<float> h_C_cpu(M * N);
    cpuMatrixMul(h_C_cpu, h_A, h_B);

    bool correct = true;
    float max_error = 1e-2;
    for (int i = 0; i < M * N; i++) {
        if (fabs(__half2float(h_C_from_gpu[i]) - h_C_cpu[i]) > max_error) {
            std::cerr << "Verification failed at index " << i << "! "
                      << "GPU result: " << __half2float(h_C_from_gpu[i])
                      << ", CPU result: " << h_C_cpu[i] << std::endl;
            correct = false;
            break;
        }
    }
    
    if (correct) {
        std::cout << "Result is correct!" << std::endl;
    }

    cudaFree(d_A);
    cudaFree(d_B);
    cudaFree(d_C);

    return 0;
}