#include <iostream>
#include <vector>
#include <cstdio>
#include <cmath>
#include <chrono>

#include <cuda_fp16.h>
#include <cuda_runtime.h>
#include <mma.h>

using namespace nvcuda;

#define M 64
#define N 16
#define K 16
#define TILE_SIZE 16
#define NUM_WARMUP 5
#define NUM_ITERATIONS 100

__global__ void matrixMulWmmaKernel(half* C, const half* A, const half* B, int m, int n, int k) {
    int blockRow = blockIdx.y;
    int blockCol = blockIdx.x;
    
    wmma::fragment<wmma::matrix_a, TILE_SIZE, TILE_SIZE, TILE_SIZE, half, wmma::row_major> a_frag;
    wmma::fragment<wmma::matrix_b, TILE_SIZE, TILE_SIZE, TILE_SIZE, half, wmma::col_major> b_frag;
    wmma::fragment<wmma::accumulator, TILE_SIZE, TILE_SIZE, TILE_SIZE, float> acc_frag;

    wmma::fill_fragment(acc_frag, 0.0f);

    __shared__ half tileA_s[TILE_SIZE * TILE_SIZE];
    __shared__ half tileB_s[TILE_SIZE * TILE_SIZE];
    
    for (int p = 0; p < k / TILE_SIZE; ++p) {
        int row = threadIdx.y;
        int col = threadIdx.x;
        
        tileA_s[row * TILE_SIZE + col] = A[(blockRow * TILE_SIZE + row) * k + (p * TILE_SIZE + col)];
        tileB_s[row * TILE_SIZE + col] = B[(p * TILE_SIZE + row) * n + (blockCol * TILE_SIZE + col)];

        __syncthreads();

        wmma::load_matrix_sync(a_frag, tileA_s, TILE_SIZE);
        wmma::load_matrix_sync(b_frag, tileB_s, TILE_SIZE);

        wmma::mma_sync(acc_frag, a_frag, b_frag, acc_frag);

        __syncthreads();
    }

    __shared__ float temp_C[TILE_SIZE * TILE_SIZE];
    int C_row = blockRow * TILE_SIZE;
    int C_col = blockCol * TILE_SIZE;
    wmma::store_matrix_sync(temp_C, acc_frag, TILE_SIZE, wmma::mem_row_major);
    
    int tid = threadIdx.y * blockDim.x + threadIdx.x;
    if (tid < TILE_SIZE * TILE_SIZE) {
        int row = tid / TILE_SIZE;
        int col = tid % TILE_SIZE;
        if (C_row + row < m && C_col + col < n) {
            C[(C_row + row) * n + (C_col + col)] = __float2half(temp_C[row * TILE_SIZE + col]);
        }
    }
}

void cpuMatrixMul(std::vector<float>& C, const std::vector<half>& A, const std::vector<half>& B) {
    for (int i = 0; i < M; ++i) {
        for (int j = 0; j < N; ++j) {
            float sum = 0.0f;
            for (int l = 0; l < K; ++l) {
                sum += __half2float(A[i * K + l]) * __half2float(B[l * N + j]);
            }
            C[i * N + j] = sum;
        }
    }
}

// Performance measurement functions
double measureGpuPerformance(half* d_A, half* d_B, half* d_C, dim3 numBlocks, dim3 threadsPerBlock) {
    // Warmup runs
    for (int i = 0; i < NUM_WARMUP; i++) {
        matrixMulWmmaKernel<<<numBlocks, threadsPerBlock>>>(d_C, d_A, d_B, M, N, K);
    }
    cudaDeviceSynchronize();

    // Create CUDA events for timing
    cudaEvent_t start, stop;
    cudaEventCreate(&start);
    cudaEventCreate(&stop);

    // Record start time
    cudaEventRecord(start);

    // Run multiple iterations for accurate timing
    for (int i = 0; i < NUM_ITERATIONS; i++) {
        matrixMulWmmaKernel<<<numBlocks, threadsPerBlock>>>(d_C, d_A, d_B, M, N, K);
    }

    // Record end time
    cudaEventRecord(stop);
    cudaEventSynchronize(stop);

    // Calculate elapsed time
    float milliseconds = 0;
    cudaEventElapsedTime(&milliseconds, start, stop);

    // Cleanup
    cudaEventDestroy(start);
    cudaEventDestroy(stop);

    return milliseconds / NUM_ITERATIONS; // Average time per iteration
}

double measureCpuPerformance(const std::vector<half>& A, const std::vector<half>& B) {
    std::vector<float> C_cpu(M * N);

    // Warmup
    for (int i = 0; i < 3; i++) {
        cpuMatrixMul(C_cpu, A, B);
    }

    // Measure CPU performance
    auto start = std::chrono::high_resolution_clock::now();

    for (int i = 0; i < 10; i++) { // Fewer iterations for CPU as it's slower
        cpuMatrixMul(C_cpu, A, B);
    }

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);

    return duration.count() / 10.0 / 1000.0; // Average time per iteration in milliseconds
}

void printPerformanceResults(double gpu_time_ms, double cpu_time_ms) {
    // Calculate FLOPS (Floating Point Operations Per Second)
    // For matrix multiplication: 2 * M * N * K operations (multiply + add for each element)
    long long total_ops = 2LL * M * N * K;

    double gpu_gflops = (total_ops / (gpu_time_ms / 1000.0)) / 1e9;
    double cpu_gflops = (total_ops / (cpu_time_ms / 1000.0)) / 1e9;

    // Calculate memory bandwidth
    // Memory access: read A (M*K), read B (K*N), write C (M*N), all in half precision (2 bytes)
    long long memory_bytes = (long long)(M * K + K * N + M * N) * sizeof(half);
    double gpu_bandwidth_gb_s = (memory_bytes / (gpu_time_ms / 1000.0)) / 1e9;

    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "                 PERFORMANCE RESULTS" << std::endl;
    std::cout << std::string(60, '=') << std::endl;

    std::cout << "Matrix Dimensions: " << M << " x " << K << " * " << K << " x " << N << " = " << M << " x " << N << std::endl;
    std::cout << "Total Operations: " << total_ops << " FLOPs" << std::endl;
    std::cout << "Memory Usage: " << memory_bytes / 1024.0 / 1024.0 << " MB" << std::endl;
    std::cout << std::string(60, '-') << std::endl;

    printf("GPU (WMMA Tensor Core):\n");
    printf("  Average Time:     %8.4f ms\n", gpu_time_ms);
    printf("  Performance:      %8.2f GFLOPS\n", gpu_gflops);
    printf("  Memory Bandwidth: %8.2f GB/s\n", gpu_bandwidth_gb_s);

    printf("\nCPU (Reference):\n");
    printf("  Average Time:     %8.4f ms\n", cpu_time_ms);
    printf("  Performance:      %8.2f GFLOPS\n", cpu_gflops);

    printf("\nSpeedup Analysis:\n");
    printf("  Time Speedup:     %8.2fx\n", cpu_time_ms / gpu_time_ms);
    printf("  GFLOPS Speedup:   %8.2fx\n", gpu_gflops / cpu_gflops);

    std::cout << std::string(60, '=') << std::endl;
}

int main() {
    int deviceId = 0;
    cudaError_t err = cudaSetDevice(deviceId);
    if (err != cudaSuccess) {
        std::cerr << "Failed to set CUDA device " << deviceId << ", error: " << cudaGetErrorString(err) << std::endl;
        return 1;
    }
    printf("Using CUDA Device %d\n", deviceId);

    std::vector<half> h_A(M * K);
    std::vector<half> h_B(K * N);
    std::vector<half> h_C_from_gpu(M * N);

    for (int i = 0; i < M * K; ++i) h_A[i] = __float2half(static_cast<float>((i % 10) + 1));
    for (int i = 0; i < K * N; ++i) h_B[i] = __float2half(static_cast<float>((i % 5) + 1));

    half *d_A, *d_B, *d_C;
    cudaMalloc(&d_A, M * K * sizeof(half));
    cudaMalloc(&d_B, K * N * sizeof(half));
    cudaMalloc(&d_C, M * N * sizeof(half));

    cudaMemcpy(d_A, h_A.data(), M * K * sizeof(half), cudaMemcpyHostToDevice);
    cudaMemcpy(d_B, h_B.data(), K * N * sizeof(half), cudaMemcpyHostToDevice);

    dim3 threadsPerBlock(TILE_SIZE, TILE_SIZE);
    dim3 numBlocks((N + TILE_SIZE - 1) / TILE_SIZE, (M + TILE_SIZE - 1) / TILE_SIZE);

    std::cout << "Launching WMMA Kernel with Grid size: (" << numBlocks.x << ", " << numBlocks.y << ")"
              << " and Block size: (" << threadsPerBlock.x << ", " << threadsPerBlock.y << ")" << std::endl;

    // First run for correctness verification
    matrixMulWmmaKernel<<<numBlocks, threadsPerBlock>>>(d_C, d_A, d_B, M, N, K);

    err = cudaGetLastError();
    if (err != cudaSuccess) {
        std::cerr << "CUDA Kernel launch error: " << cudaGetErrorString(err) << std::endl;
        return 1;
    }
    cudaDeviceSynchronize();

    // Performance measurement
    std::cout << "\nMeasuring GPU performance..." << std::endl;
    double gpu_time_ms = measureGpuPerformance(d_A, d_B, d_C, numBlocks, threadsPerBlock);

    std::cout << "Measuring CPU performance..." << std::endl;
    double cpu_time_ms = measureCpuPerformance(h_A, h_B);

    cudaMemcpy(h_C_from_gpu.data(), d_C, M * N * sizeof(half), cudaMemcpyDeviceToHost);

    std::cout << "\nVerifying result..." << std::endl;
    std::vector<float> h_C_cpu(M * N);
    cpuMatrixMul(h_C_cpu, h_A, h_B);

    // Verify correctness (simplified)
    bool correct = true;
    float max_error = 1e-2;
    float total_error = 0.0f;
    int error_count = 0;

    for (int i = 0; i < M * N; i++) {
        float gpu_val = __half2float(h_C_from_gpu[i]);
        float cpu_val = h_C_cpu[i];
        float error = fabs(gpu_val - cpu_val);
        total_error += error;

        if (error > max_error) {
            if (error_count < 5) { // Only show first 5 errors
                std::cerr << "Error at [" << i/N << "," << i%N << "]: "
                          << "GPU=" << gpu_val << ", CPU=" << cpu_val
                          << ", Error=" << error << std::endl;
            }
            correct = false;
            error_count++;
        }
    }

    std::cout << "Verification: " << (correct ? "✓ PASSED" : "✗ FAILED") << std::endl;
    if (!correct) {
        std::cout << "Errors found: " << error_count << "/" << M * N << " elements" << std::endl;
    }

    // Display performance results
    printPerformanceResults(gpu_time_ms, cpu_time_ms);

    cudaFree(d_A);
    cudaFree(d_B);
    cudaFree(d_C);

    return 0;
}
