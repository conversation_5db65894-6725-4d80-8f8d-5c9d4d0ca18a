# Try to find Visual Studio installations
Write-Host "Searching for Visual Studio installations..."

# Check common VS 2022 locations
$vs2022Paths = @(
    "C:\Program Files\Microsoft Visual Studio\2022\Community",
    "C:\Program Files\Microsoft Visual Studio\2022\Professional", 
    "C:\Program Files\Microsoft Visual Studio\2022\Enterprise",
    "C:\Program Files (x86)\Microsoft Visual Studio\2022\Community",
    "C:\Program Files (x86)\Microsoft Visual Studio\2022\Professional",
    "C:\Program Files (x86)\Microsoft Visual Studio\2022\Enterprise"
)

foreach ($path in $vs2022Paths) {
    if (Test-Path $path) {
        Write-Host "Found VS 2022 at: $path"
        $vcvarsPath = Join-Path $path "VC\Auxiliary\Build\vcvarsall.bat"
        if (Test-Path $vcvarsPath) {
            Write-Host "vcvarsall.bat found at: $vcvarsPath"
        }
    }
}

# Check VS 2019 locations
$vs2019Paths = @(
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community",
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional",
    "C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise"
)

foreach ($path in $vs2019Paths) {
    if (Test-Path $path) {
        Write-Host "Found VS 2019 at: $path"
        $vcvarsPath = Join-Path $path "VC\Auxiliary\Build\vcvarsall.bat"
        if (Test-Path $vcvarsPath) {
            Write-Host "vcvarsall.bat found at: $vcvarsPath"
        }
    }
}

# Try to find using vswhere
$vswherePath = "C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe"
if (Test-Path $vswherePath) {
    Write-Host "Using vswhere to find VS installations:"
    & $vswherePath -latest -property installationPath
}
