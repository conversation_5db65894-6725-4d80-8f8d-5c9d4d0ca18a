#include <iostream>
#include <vector>
#include <cstdio>
#include <cmath>
#include <chrono>

#include <cuda_fp16.h>
#include <cuda_runtime.h>
#include <mma.h>

using namespace nvcuda;

#define TILE_SIZE 16
#define NUM_WARMUP 5
#define NUM_ITERATIONS 50

// WMMA Tensor Core Kernel (parameterized)
__global__ void matrixMulWmmaKernel(half* C, const half* A, const half* B, int m, int n, int k) {
    int blockRow = blockIdx.y;
    int blockCol = blockIdx.x;
    
    wmma::fragment<wmma::matrix_a, TILE_SIZE, TILE_SIZE, TILE_SIZE, half, wmma::row_major> a_frag;
    wmma::fragment<wmma::matrix_b, TILE_SIZE, TILE_SIZE, TILE_SIZE, half, wmma::col_major> b_frag;
    wmma::fragment<wmma::accumulator, TILE_SIZE, TILE_SIZE, TILE_SIZE, float> acc_frag;

    wmma::fill_fragment(acc_frag, 0.0f);

    __shared__ half tileA_s[TILE_SIZE * TILE_SIZE];
    __shared__ half tileB_s[TILE_SIZE * TILE_SIZE];
    
    for (int p = 0; p < k / TILE_SIZE; ++p) {
        int row = threadIdx.y;
        int col = threadIdx.x;
        
        if (blockRow * TILE_SIZE + row < m && p * TILE_SIZE + col < k) {
            tileA_s[row * TILE_SIZE + col] = A[(blockRow * TILE_SIZE + row) * k + (p * TILE_SIZE + col)];
        } else {
            tileA_s[row * TILE_SIZE + col] = __float2half(0.0f);
        }
        
        if (p * TILE_SIZE + row < k && blockCol * TILE_SIZE + col < n) {
            tileB_s[row * TILE_SIZE + col] = B[(p * TILE_SIZE + row) * n + (blockCol * TILE_SIZE + col)];
        } else {
            tileB_s[row * TILE_SIZE + col] = __float2half(0.0f);
        }

        __syncthreads();

        wmma::load_matrix_sync(a_frag, tileA_s, TILE_SIZE);
        wmma::load_matrix_sync(b_frag, tileB_s, TILE_SIZE);

        wmma::mma_sync(acc_frag, a_frag, b_frag, acc_frag);

        __syncthreads();
    }

    __shared__ float temp_C[TILE_SIZE * TILE_SIZE];
    int C_row = blockRow * TILE_SIZE;
    int C_col = blockCol * TILE_SIZE;
    wmma::store_matrix_sync(temp_C, acc_frag, TILE_SIZE, wmma::mem_row_major);
    
    int tid = threadIdx.y * blockDim.x + threadIdx.x;
    if (tid < TILE_SIZE * TILE_SIZE) {
        int row = tid / TILE_SIZE;
        int col = tid % TILE_SIZE;
        if (C_row + row < m && C_col + col < n) {
            C[(C_row + row) * n + (C_col + col)] = __float2half(temp_C[row * TILE_SIZE + col]);
        }
    }
}

double benchmarkMatrixSize(int M, int N, int K) {
    std::cout << "\nBenchmarking " << M << "x" << K << " * " << K << "x" << N << " = " << M << "x" << N << std::endl;
    
    // Allocate host memory
    std::vector<half> h_A(M * K);
    std::vector<half> h_B(K * N);
    std::vector<half> h_C(M * N);

    // Initialize matrices
    for (int i = 0; i < M * K; ++i) h_A[i] = __float2half(static_cast<float>((i % 10) + 1));
    for (int i = 0; i < K * N; ++i) h_B[i] = __float2half(static_cast<float>((i % 5) + 1));

    // Allocate device memory
    half *d_A, *d_B, *d_C;
    cudaMalloc(&d_A, M * K * sizeof(half));
    cudaMalloc(&d_B, K * N * sizeof(half));
    cudaMalloc(&d_C, M * N * sizeof(half));

    // Copy data to device
    cudaMemcpy(d_A, h_A.data(), M * K * sizeof(half), cudaMemcpyHostToDevice);
    cudaMemcpy(d_B, h_B.data(), K * N * sizeof(half), cudaMemcpyHostToDevice);

    // Setup kernel launch parameters
    dim3 threadsPerBlock(TILE_SIZE, TILE_SIZE);
    dim3 numBlocks((N + TILE_SIZE - 1) / TILE_SIZE, (M + TILE_SIZE - 1) / TILE_SIZE);

    // Warmup
    for (int i = 0; i < NUM_WARMUP; i++) {
        matrixMulWmmaKernel<<<numBlocks, threadsPerBlock>>>(d_C, d_A, d_B, M, N, K);
    }
    cudaDeviceSynchronize();

    // Benchmark
    cudaEvent_t start, stop;
    cudaEventCreate(&start);
    cudaEventCreate(&stop);

    cudaEventRecord(start);
    for (int i = 0; i < NUM_ITERATIONS; i++) {
        matrixMulWmmaKernel<<<numBlocks, threadsPerBlock>>>(d_C, d_A, d_B, M, N, K);
    }
    cudaEventRecord(stop);
    cudaEventSynchronize(stop);

    float milliseconds = 0;
    cudaEventElapsedTime(&milliseconds, start, stop);
    double avg_time_ms = milliseconds / NUM_ITERATIONS;

    // Calculate performance metrics
    long long total_ops = 2LL * M * N * K;
    double gflops = (total_ops / (avg_time_ms / 1000.0)) / 1e9;
    long long memory_bytes = (long long)(M * K + K * N + M * N) * sizeof(half);
    double bandwidth_gb_s = (memory_bytes / (avg_time_ms / 1000.0)) / 1e9;

    printf("  Time: %8.4f ms, Performance: %8.2f GFLOPS, Bandwidth: %8.2f GB/s\n", 
           avg_time_ms, gflops, bandwidth_gb_s);

    // Cleanup
    cudaEventDestroy(start);
    cudaEventDestroy(stop);
    cudaFree(d_A);
    cudaFree(d_B);
    cudaFree(d_C);

    return gflops;
}

int main() {
    int deviceId = 0;
    cudaSetDevice(deviceId);
    printf("Using CUDA Device %d\n", deviceId);

    // Get device properties
    cudaDeviceProp prop;
    cudaGetDeviceProperties(&prop, deviceId);
    printf("Device: %s\n", prop.name);
    printf("Compute Capability: %d.%d\n", prop.major, prop.minor);
    printf("Memory: %.2f GB\n", prop.totalGlobalMem / 1024.0 / 1024.0 / 1024.0);

    std::cout << "\n" << std::string(80, '=') << std::endl;
    std::cout << "                    WMMA TENSOR CORE BENCHMARK" << std::endl;
    std::cout << std::string(80, '=') << std::endl;

    // Test different matrix sizes
    struct TestCase {
        int M, N, K;
        const char* description;
    };

    TestCase test_cases[] = {
        {64, 16, 16, "Small (Original)"},
        {128, 128, 128, "Medium Square"},
        {256, 256, 256, "Large Square"},
        {512, 512, 512, "Very Large Square"},
        {1024, 64, 1024, "Tall Matrix"},
        {64, 1024, 1024, "Wide Matrix"},
        {2048, 2048, 64, "Shallow but Large"}
    };

    double max_gflops = 0.0;
    int best_case = 0;

    for (int i = 0; i < sizeof(test_cases) / sizeof(TestCase); i++) {
        double gflops = benchmarkMatrixSize(test_cases[i].M, test_cases[i].N, test_cases[i].K);
        if (gflops > max_gflops) {
            max_gflops = gflops;
            best_case = i;
        }
    }

    std::cout << "\n" << std::string(80, '=') << std::endl;
    printf("Best Performance: %.2f GFLOPS (%s: %dx%dx%d)\n", 
           max_gflops, test_cases[best_case].description,
           test_cases[best_case].M, test_cases[best_case].N, test_cases[best_case].K);
    std::cout << std::string(80, '=') << std::endl;

    return 0;
}
