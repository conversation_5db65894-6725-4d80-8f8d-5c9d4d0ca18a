# Comprehensive Visual Studio installation checker
Write-Host "=== Visual Studio Installation Analysis ===" -ForegroundColor Green

# Check using vswhere (most reliable method)
$vswherePath = "C:\Program Files (x86)\Microsoft Visual Studio\Installer\vswhere.exe"
if (Test-Path $vswherePath) {
    Write-Host "`n1. Using vswhere to find all VS installations:" -ForegroundColor Yellow
    try {
        $installations = & $vswherePath -all -format json | ConvertFrom-Json
        foreach ($install in $installations) {
            Write-Host "  - $($install.displayName)" -ForegroundColor Cyan
            Write-Host "    Version: $($install.installationVersion)"
            Write-Host "    Path: $($install.installationPath)"
            Write-Host "    Product ID: $($install.productId)"
            Write-Host ""
        }
    } catch {
        Write-Host "  Error running vswhere: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "vswhere.exe not found" -ForegroundColor Red
}

# Check common installation paths
Write-Host "2. Checking common VS installation paths:" -ForegroundColor Yellow

$commonPaths = @(
    "C:\Program Files (x86)\Microsoft Visual Studio 14.0",
    "C:\Program Files (x86)\Microsoft Visual Studio\2017",
    "C:\Program Files (x86)\Microsoft Visual Studio\2019", 
    "C:\Program Files (x86)\Microsoft Visual Studio\2022",
    "C:\Program Files\Microsoft Visual Studio\2017",
    "C:\Program Files\Microsoft Visual Studio\2019",
    "C:\Program Files\Microsoft Visual Studio\2022",
    "e:\Program Files\Microsoft Visual Studio\2022"
)

foreach ($path in $commonPaths) {
    if (Test-Path $path) {
        Write-Host "  ✓ Found: $path" -ForegroundColor Green
        
        # Try to get more details
        $editions = Get-ChildItem $path -Directory -ErrorAction SilentlyContinue
        foreach ($edition in $editions) {
            Write-Host "    Edition: $($edition.Name)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "  ✗ Not found: $path" -ForegroundColor Gray
    }
}

# Check installed programs via registry
Write-Host "`n3. Checking Windows Programs and Features:" -ForegroundColor Yellow
try {
    $programs = Get-ItemProperty "HKLM:\Software\Microsoft\Windows\CurrentVersion\Uninstall\*" | 
                Where-Object { $_.DisplayName -like "*Visual Studio*" } |
                Select-Object DisplayName, DisplayVersion, UninstallString
    
    foreach ($program in $programs) {
        Write-Host "  - $($program.DisplayName)" -ForegroundColor Cyan
        Write-Host "    Version: $($program.DisplayVersion)"
        if ($program.UninstallString) {
            Write-Host "    Uninstall: $($program.UninstallString)"
        }
        Write-Host ""
    }
} catch {
    Write-Host "  Error checking registry: $($_.Exception.Message)" -ForegroundColor Red
}

# Check for VS Installer
Write-Host "4. Visual Studio Installer status:" -ForegroundColor Yellow
$installerPath = "C:\Program Files (x86)\Microsoft Visual Studio\Installer\vs_installer.exe"
if (Test-Path $installerPath) {
    Write-Host "  ✓ VS Installer found: $installerPath" -ForegroundColor Green
} else {
    Write-Host "  ✗ VS Installer not found" -ForegroundColor Red
}

Write-Host "`n=== Recommendations ===" -ForegroundColor Green
Write-Host "Based on the analysis above, I'll provide specific uninstall recommendations."
